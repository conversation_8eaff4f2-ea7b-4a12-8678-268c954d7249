/**
 * Google Apps Script for TradingView Alert Webhook Integration
 * Receives JSON alerts from Pine Script and logs them to Google Sheets
 */

// Configuration
const SHEET_NAME = "TLF_Alerts";
const TIMEZONE = "America/New_York"; // Adjust to your timezone

function doPost(e) {
  try {
    // Parse the incoming JSON data
    const data = JSON.parse(e.postData.contents);
    
    // Log the alert to Google Sheets
    logAlert(data);
    
    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        status: "success",
        message: "Alert logged successfully",
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    // Log error and return error response
    console.error("Error processing webhook:", error);
    
    return ContentService
      .createTextOutput(JSON.stringify({
        status: "error",
        message: error.toString(),
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function logAlert(alertData) {
  // Get or create the spreadsheet
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  let sheet = spreadsheet.getSheetByName(SHEET_NAME);
  
  // Create sheet if it doesn't exist
  if (!sheet) {
    sheet = spreadsheet.insertSheet(SHEET_NAME);
    
    // Add headers
    const headers = [
      "Timestamp",
      "Trade Ref",
      "Event Type", 
      "Side",
      "Symbol",
      "Price",
      "Quantity",
      "Strategy",
      "Raw JSON"
    ];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    
    // Format headers
    const headerRange = sheet.getRange(1, 1, 1, headers.length);
    headerRange.setFontWeight("bold");
    headerRange.setBackground("#4285f4");
    headerRange.setFontColor("white");
  }
  
  // Prepare row data
  const rowData = [
    new Date(), // Current timestamp
    alertData.trade_ref || "",
    alertData.event_type || "",
    alertData.side || "",
    alertData.symbol || "",
    parseFloat(alertData.price) || 0,
    parseFloat(alertData.quantity) || 0,
    alertData.strategy || "",
    JSON.stringify(alertData) // Raw JSON for debugging
  ];
  
  // Add the new row
  sheet.appendRow(rowData);
  
  // Auto-resize columns
  sheet.autoResizeColumns(1, rowData.length);
  
  // Add conditional formatting for event types
  formatEventTypes(sheet);
  
  console.log("Alert logged:", alertData);
}

function formatEventTypes(sheet) {
  const dataRange = sheet.getDataRange();
  const eventTypeColumn = 3; // Column C (Event Type)
  
  // Clear existing conditional formatting
  dataRange.clearFormat();
  
  // Define color rules for different event types
  const rules = [
    {
      condition: SpreadsheetApp.newConditionalFormatRule()
        .whenTextEqualTo("ENTRY")
        .setBackground("#e1f5fe")
        .setRanges([sheet.getRange(2, eventTypeColumn, sheet.getLastRow() - 1, 1)])
        .build()
    },
    {
      condition: SpreadsheetApp.newConditionalFormatRule()
        .whenTextContains("TP")
        .setBackground("#e8f5e8")
        .setRanges([sheet.getRange(2, eventTypeColumn, sheet.getLastRow() - 1, 1)])
        .build()
    },
    {
      condition: SpreadsheetApp.newConditionalFormatRule()
        .whenTextEqualTo("SL")
        .setBackground("#ffebee")
        .setRanges([sheet.getRange(2, eventTypeColumn, sheet.getLastRow() - 1, 1)])
        .build()
    },
    {
      condition: SpreadsheetApp.newConditionalFormatRule()
        .whenTextEqualTo("BE")
        .setBackground("#fff3e0")
        .setRanges([sheet.getRange(2, eventTypeColumn, sheet.getLastRow() - 1, 1)])
        .build()
    },
    {
      condition: SpreadsheetApp.newConditionalFormatRule()
        .whenTextEqualTo("FORCE_CLOSE")
        .setBackground("#f3e5f5")
        .setRanges([sheet.getRange(2, eventTypeColumn, sheet.getLastRow() - 1, 1)])
        .build()
    }
  ];
  
  // Apply the rules
  sheet.setConditionalFormatRules(rules);
}

// Test function to verify the setup
function testWebhook() {
  const testData = {
    trade_ref: "TEST_1",
    event_type: "ENTRY",
    side: "LONG",
    symbol: "BTCUSDT",
    price: "50000.12345",
    quantity: "0.0200",
    timestamp: "2024-01-15 14:30:45",
    strategy: "Temp V3.1"
  };
  
  logAlert(testData);
  console.log("Test alert logged successfully");
}

// Function to create analysis dashboard
function createAnalysisDashboard() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  let dashboardSheet = spreadsheet.getSheetByName("Analysis_Dashboard");
  
  if (!dashboardSheet) {
    dashboardSheet = spreadsheet.insertSheet("Analysis_Dashboard");
    
    // Add analysis formulas and charts
    const analysisHeaders = [
      "Metric", "Value", "Formula"
    ];
    
    dashboardSheet.getRange(1, 1, 1, 3).setValues([analysisHeaders]);
    
    // Add key metrics
    const metrics = [
      ["Total Alerts", "", `=COUNTA(${SHEET_NAME}!B:B)-1`],
      ["Entry Alerts", "", `=COUNTIF(${SHEET_NAME}!C:C,"ENTRY")`],
      ["TP Alerts", "", `=SUMPRODUCT(--(ISNUMBER(SEARCH("TP",${SHEET_NAME}!C:C))))`],
      ["SL Alerts", "", `=COUNTIF(${SHEET_NAME}!C:C,"SL")`],
      ["BE Alerts", "", `=COUNTIF(${SHEET_NAME}!C:C,"BE")`],
      ["Force Close Alerts", "", `=COUNTIF(${SHEET_NAME}!C:C,"FORCE_CLOSE")`],
      ["Avg Alert Rate (per hour)", "", `=IF(ROWS(${SHEET_NAME}!A:A)>1,(COUNTA(${SHEET_NAME}!A:A)-1)/((MAX(${SHEET_NAME}!A:A)-MIN(${SHEET_NAME}!A:A))*24),0)`]
    ];
    
    dashboardSheet.getRange(2, 1, metrics.length, 3).setValues(metrics);
    
    // Format the dashboard
    dashboardSheet.getRange(1, 1, 1, 3).setFontWeight("bold").setBackground("#4285f4").setFontColor("white");
    dashboardSheet.autoResizeColumns(1, 3);
  }
  
  return dashboardSheet;
}
